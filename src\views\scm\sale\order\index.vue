<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <!-- 基础搜索字段 -->
      <el-form-item label="订单编号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入订单编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="客户" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入产品名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!-- 扩展搜索字段 -->
      <template v-if="showAdvanced">
        <el-form-item label="产品编码" prop="productCode">
          <el-input
            v-model="queryParams.productCode"
            placeholder="请输入产品编码"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="支付状态" prop="paymentStatus">
          <el-select
            v-model="queryParams.paymentStatus"
            placeholder="请选择支付状态"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.PAYMENT_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="订单日期" prop="orderDate">
          <el-date-picker
            v-model="queryParams.orderDate"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <!-- <el-form-item label="审批状态" prop="approvalStatus">
          <el-select
            v-model="queryParams.approvalStatus"
            placeholder="请选择审批状态"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.APPROVE_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item> -->
        <el-form-item label="来源单号" prop="orderSource">
          <el-input
            v-model="queryParams.orderSource"
            placeholder="请输入来源单号"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="发票状态" prop="invoiceStatus">
          <el-select
            v-model="queryParams.invoiceStatus"
            placeholder="请选择发票状态"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.INVOICE_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="生产状态" prop="mfgStatus">
          <el-select
            v-model="queryParams.mfgStatus"
            placeholder="请选择生产状态"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.MFG_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="结案状态" prop="closeStatus">
          <el-select
            v-model="queryParams.closeStatus"
            placeholder="请选择结案状态"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.CLOSE_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="销售员" prop="salemanId">
          <el-select
            v-model="queryParams.salemanId"
            placeholder="请选择销售员"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="user in userList"
              :key="user.id"
              :label="user.nickname"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
            v-model="queryParams.createTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
      </template>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['sale:order:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['sale:order:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
        <el-button
          type="primary"
          plain
          @click="handleApproval"
          :loading="exportLoading"
          v-hasPermi="['sale:order:approve']"
        >
          <Icon icon="ep:check" class="mr-5px" /> 审核
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleApproval"
          :loading="exportLoading"
          v-hasPermi="['sale:order:approve']"
        >
          <Icon icon="ep:finished" class="mr-5px" /> 结案
        </el-button>
        <el-button
          type="primary"
          plain
          @click="handlePrint"
          :loading="exportLoading"
          v-hasPermi="['sale:order:print']"
        >
          <Icon icon="ep:finished" class="mr-5px" /> 打印
        </el-button>
        <el-button
          type="primary"
          link
          @click="showAdvanced = !showAdvanced"
        >
          <template v-if="showAdvanced">
            <Icon icon="ep:arrow-up" class="mr-5px" /> 收起
          </template>
          <template v-else>
            <Icon icon="ep:arrow-down" class="mr-5px" /> 展开
          </template>
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="flattenedList"
      :stripe="true"
      border
      :show-overflow-tooltip="true"
      highlight-current-row
      show-summary
      :summary-method="summaryMethod"
      @current-change="handleCurrentChange"
      @selection-change="handleSelectionChange"
      :span-method="objectSpanMethod"
    >
      <el-table-column type="selection" width="60" :selectable="checkSelectable"/>
      <el-table-column label="订单编号" align="left" width="200px" prop="orderNo">
        <template #default="scope">
          <div class="order-no-container">
            <div class="order-no-cell">
              <div class="order-no-content">
                <span
                  class="order-no-link"
                  @click="openDetailDialog(scope.row.orderId || scope.row.id)"
                  title="点击查看订单详情"
                >
                  {{ scope.row.orderNo || '-' }}
                </span>
              </div>
              <el-button
                link
                type="info"
                @click="copyOrderNo(scope.row.orderNo)"
                class="copy-btn copy-btn-fixed"
                size="small"
                v-if="scope.row.orderNo"
              >
                <Icon icon="ep:copy-document" />
              </el-button>
            </div>
            <!-- 主订单级别状态 -->
            <div class="status-tags" v-if="scope.row.orderNo">
              <dict-tag
                v-if="scope.row.approvalStatus"
                :type="DICT_TYPE.APPROVE_STATUS"
                :value="scope.row.approvalStatus"
                class="status-tag"
              />
              <dict-tag
                v-if="scope.row.closeStatus"
                :type="DICT_TYPE.CLOSE_STATUS"
                :value="scope.row.closeStatus"
                class="status-tag"
              />
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="客户姓名" align="left" prop="customerName" width="180px"/>
      <el-table-column label="订单时间" align="center" prop="orderDate" :formatter="dateFormatter2" width="100px"/>
      <!-- 商品信息列 -->
      <el-table-column label="商品名称" align="left" prop="detail.productName" width="180px" />
      <el-table-column label="商品编号" align="left" prop="detail.productCode" width="120px" />
      <el-table-column label="规格" align="left" prop="detail.spec"/>
      <el-table-column label="数量" align="left" prop="detail.quantity" :formatter="quantityTableFormatter" width="130">
        <template #default="scope">
          <span>{{ quantityTableFormatter(null, null, scope.row.detail.quantity, null) }}{{ unitMap[scope.row.detail.unit] || '' }}</span>
        </template>
      </el-table-column>
      <el-table-column label='规格数量' align="left" prop="detail.specQuantityTotal" width='120'/>
      <el-table-column label="不含税单价" align="left" prop="detail.noTaxUnitPrice" width="110" :formatter="amountTableFormatter"/>
      <el-table-column label="含税单价" align="left" prop="detail.unitPrice" width="110" :formatter="amountTableFormatter"/>
      <el-table-column label="税率%" align="left" prop="detail.taxRate"/>
      <el-table-column label="税额" align="left" prop="detail.tax" width="110" :formatter="amountTableFormatter"/>
      <el-table-column label="含税总价" align="left" prop="detail.total" width="110" :formatter="amountTableFormatter"/>
      <el-table-column label="不含税总价" align="left" prop="detail.noTaxTotal" width="110" :formatter="amountTableFormatter"/>
      <el-table-column label="备注" align="left" prop="detail.remark" width="150px"/>

      <el-table-column label="交货日期" align="center" prop="detail.deliveryDate" :formatter="dateFormatter2" width="100px"/>
      <el-table-column label="库存数量" align="center" prop="detail.inventoryQuantity" width="100px" :formatter="quantityTableFormatter"/>
      <el-table-column label="锁库数量" align="center" prop="detail.lockedQuantity" width="100px" :formatter="quantityTableFormatter"/>
      <el-table-column label="发货数量" align="center" prop="detail.deliveredQuantity" width="100px" :formatter="quantityTableFormatter"/>
      <el-table-column label="生产数量" align="center" prop="detail.producedQuantity" width="100px" :formatter="quantityTableFormatter"/>
      <el-table-column label="生产状态" align="center" prop="detail.productionStatus" width="100px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.MFG_STATUS" :value="scope.row.detail.productionStatus" />
        </template>
      </el-table-column>
      <el-table-column label="发票状态" align="center" prop="detail.invoiceStatus" width="100px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.INVOICE_STATUS" :value="scope.row.detail.invoiceStatus" />
        </template>
      </el-table-column>
      <el-table-column label="发货状态" align="center" prop="detail.deliveryStatus" width="100px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.DELIVERY_STATUS" :value="scope.row.detail.deliveryStatus" />
        </template>
      </el-table-column>
      <el-table-column label="付款状态" align="center" prop="detail.paymentStatus" width="100px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.PAYMENT_STATUS" :value="scope.row.paymentStatus" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="审批编号" align="center" prop="approvalNo" width="120px"/> -->
      <!-- <el-table-column label="审批人" align="center" prop="approverName" width="100px"/> -->
      <!-- 其他订单信息列 -->
      <el-table-column label="配送地址" align="center" prop="shippingAddress" width="180px"/>
      <el-table-column label="账单地址" align="center" prop="billingAddress" width="180px"/>
      <el-table-column label="订单备注" align="center" prop="remarks" width="180px"/>
      <el-table-column label="来源单号" align="center" prop="orderSource" width="120px">
        <template #default="scope">
          <div class="order-no-cell" v-if="scope.row.orderSource">
            <div class="order-no-content">
              <span>{{ scope.row.orderSource }}</span>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.orderSource)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" />
            </el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="物流单号" align="center" prop="trackingNumber" width="120px">
        <template #default="scope">
          <div class="order-no-cell" v-if="scope.row.trackingNumber">
            <div class="order-no-content">
              <span>{{ scope.row.trackingNumber }}</span>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.trackingNumber)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" />
            </el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="支付方式" align="center" prop="paymentMethod" width="100px"> 
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.PAYMENT_TYPE" :value="scope.row.paymentMethod" />
        </template>
      </el-table-column>

      <el-table-column label="销售员" align="center" prop="salemanName" width="100px"/>
      <el-table-column label="销售员部门" align="center" prop="salemanDeptName" width="100px"/>
      <el-table-column label="创建时间" align="center" prop="createTime" :formatter="dateFormatter" width="180px"/>
      <el-table-column label="创建者" align="center" prop="creator">
        <template #default="scope">
          <span>{{ userMap[scope.row.creator] || scope.row.creator}}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" min-width="230px" fixed="right" prop="operation">
        <template #default="scope">

           <el-button
            link
            type="primary"
            @click="openDeliveryNoticeForm('create', scope.row.id)"
            v-hasPermi="['sale:order:notice']"
          >
            发货通知
          </el-button>
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['sale:order:update']"
            v-if="scope.row.approvalStatus < '3'"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['sale:order:delete']"
            v-if="scope.row.approvalStatus < '3'"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <OrderForm ref="formRef" @success="getList" />
  <DeliveryNoticeForm ref="deliveryNoticeFormRef" @success="getList" />
  <ApproveInfoForm ref="approveInfoFormRef" @success="getList" :biz-id="currentRow?.id" biz-type="sale_order" :biz-no="currentRow?.orderNo"/>

  <!-- 子表的列表 -->
  <!-- <ContentWrap>
    <el-tabs model-value="orderDetail">
      <el-tab-pane label="订单商品" name="orderDetail">
        <OrderDetailList :orderId="currentRow?.id" />
      </el-tab-pane>
    </el-tabs>
  </ContentWrap> -->
</template>

<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter, dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import { OrderApi, OrderVO } from '@/api/scm/sale/order'
import OrderForm from './OrderForm.vue'
import { UnitApi } from '@/api/scm/base/unit'
import {getSimpleUserList, UserVO} from '@/api/system/user'
import DeliveryNoticeForm from '../deliverynotice/DeliveryNoticeForm.vue'
import ApproveInfoForm from '../../base/approveinfo/ApproveInfoForm.vue'
import { getAccessToken, getTenantId } from '@/utils/auth'

import { amountTableFormatter, quantityTableFormatter, formatAmount, formatQuantity } from '@/utils/formatter'
import { useClipboard } from '@vueuse/core'
/** 销售订单 列表 */
defineOptions({ name: 'SaleOrder' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter() // 路由
const { copy } = useClipboard() // 复制功能

/** 复制订单单号 */
const copyOrderNo = async (orderNo: string) => {
  try {
    await copy(orderNo)
    message.success('单号复制成功')
  } catch (error) {
    message.error('复制失败')
  }
}

const loading = ref(true) // 列表的加载中
const list = ref<OrderVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const showAdvanced = ref(false) // 是否显示高级搜索
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  orderNo: undefined,
  customerName: undefined,
  productName: undefined,
  productCode: undefined,
  orderDate: [],
  approvalStatus: undefined,
  orderStatus: undefined,
  paymentStatus: undefined,
  orderSource: undefined,
  paymentMethod: undefined,
  invoiceStatus: undefined,
  mfgStatus: undefined,
  closeStatus: undefined,
  salemanId: undefined,
  salemanName: undefined,
  salemanDeptId: undefined,
  salemanDeptName: undefined,
  createTime: [],
  detail: true
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const approveInfoFormRef = ref()
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await OrderApi.getOrderPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const deliveryNoticeFormRef = ref()


const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

const openDeliveryNoticeForm = (type: string, orderId: number) => {
  deliveryNoticeFormRef.value.open(type, undefined, orderId)
}

/** 打开详情页面 */
const openDetailDialog = (orderId: number) => {
  // 查找对应的订单数据
  const orderData = list.value.find(item => (item.orderId || item.id) === orderId)

  router.push({
    name: 'SaleOrderDetail',
    params: { id: orderId.toString() },
    query: {
      title: orderData?.orderNo || '销售订单详情' // 传递订单编号作为标题
    }
  })
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await OrderApi.deleteOrder(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await OrderApi.exportOrder(queryParams)
    download.excel(data, '销售订单.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 选中行操作 */
const currentRow = ref<any>({}) // 选中行
const selectedRows = ref<any[]>([]) // 选中的多行数据
const handleCurrentChange = (row: any) => {
  currentRow.value = row
}

/** 处理表格选择变化 */
const handleSelectionChange = (selection: any[]) => {
  // 由于表格有行合并，需要去重处理，只保留主订单行
  const uniqueSelection: any[] = []
  const orderNoSet = new Set()

  selection.forEach(row => {
    if (!orderNoSet.has(row.orderNo)) {
      orderNoSet.add(row.orderNo)
      uniqueSelection.push(row)
    }
  })

  selectedRows.value = uniqueSelection
}

/** 检查行是否可选择 */
const checkSelectable = (row: any, index: number) => {
  // 只有主订单行（第一个明细行）可以选择
  const currentOrderNo = row.orderNo
  const previousRow = flattenedList.value[index - 1]

  // 如果是第一行，或者与前一行的订单编号不同，则可选择
  return index === 0 || !previousRow || previousRow.orderNo !== currentOrderNo
}

// 新增：定义合并行数存储数组
const spanArr = ref<number[]>([])

const flattenedList = computed(() => {
  const result: any[] = []
  spanArr.value = [] // 每次重新计算时清空旧数据
  list.value.forEach(order => {
    const details = order.orderDetails?.length ? order.orderDetails : [{}] // 确保无明细时也有占位行
    const detailCount = details.length
    details.forEach((detail, index) => {
      result.push({ ...order, detail })
      // 主信息列只在第一个明细行合并，合并行数=明细数量
      spanArr.value.push(index === 0 ? detailCount : 0)
    })
  })
  return result
})

// 需要合并的主信息列（必须与el-table-column的prop严格匹配）
const mergeFields = [
  'orderNo', 'customerName', 'orderDate', 'approvalNo', 'approvalStatus',
  'approverName', 'shippingAddress', 'billingAddress', 'remarks', 'orderSource',
  'trackingNumber', 'paymentMethod', 'closeStatus', 'salemanName', 'salemanDeptName',
  'createTime', 'creator', 'operation'
]

const objectSpanMethod = ({ row, column, rowIndex }: { row: any, column: any, rowIndex: number }) => {
  // 处理选择列和主信息列合并
  if (column.type === 'selection' || mergeFields.includes(column.property)) {
    const span = spanArr.value[rowIndex]
    return {
      rowspan: span, // 合并行数
      colspan: span > 0 ? 1 : 0 // 0表示隐藏单元格
    }
  }
  // 商品明细列不合并
  return { rowspan: 1, colspan: 1 }
}

/** 表格汇总方法 */
const summaryMethod = ({ columns, data }: { columns: any[], data: any[] }) => {
  const sums: any[] = []

  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 需要汇总的数量字段
    const quantityFields = ['detail.quantity', 'detail.inventoryQuantity', 'detail.lockedQuantity',
                           'detail.deliveredQuantity', 'detail.producedQuantity']

    // 需要汇总的金额字段
    const amountFields = ['detail.noTaxUnitPrice', 'detail.unitPrice', 'detail.tax',
                         'detail.total', 'detail.noTaxTotal']

    if (quantityFields.includes(column.property)) {
      // 数量字段汇总
      const values = data.map(item => {
        const value = column.property.split('.').reduce((obj, key) => obj?.[key], item)
        return Number(value) || 0
      })
      const total = values.reduce((prev, curr) => prev + curr, 0)
      sums[index] = formatQuantity(total)
    } else if (amountFields.includes(column.property)) {
      // 金额字段汇总
      const values = data.map(item => {
        const value = column.property.split('.').reduce((obj, key) => obj?.[key], item)
        return Number(value) || 0
      })
      const total = values.reduce((prev, curr) => prev + curr, 0)
      sums[index] = formatAmount(total)
    } else {
      // 其他字段不汇总
      sums[index] = ''
    }
  })

  return sums
}

const unitMap = ref({}) // 单位的映射
const getUnitMap = async () => {
  const data = await UnitApi.getUnitPage({ pageSize: 100 })
  unitMap.value = data.list.reduce((map, item) => {
    map[item.id] = item.name
    return map
  }, {})
}

const userList = ref<UserVO[]>([]) // 用户列表
const userMap = ref({}) // 用户的映射
const getUserList = async () => {
  const data = await getSimpleUserList()
  userList.value = data
  userMap.value = data.reduce((map, item) => {
    map[item.id] = item.nickname
    return map
  }, {})
}





const handleApproval = async () => {
  if(!selectedRows.value || selectedRows.value.length === 0) {
    message.error('请选择要审核的订单！')
    return
  }

  // 如果选中了多个订单，提示用户
  if(selectedRows.value.length > 1) {
    message.warning('当前只支持单个订单审核，请选择一个订单进行审核')
    return
  }

  // 设置当前行为选中的第一个订单
  const selectedOrder = selectedRows.value[0]
  currentRow.value = selectedOrder

  // 发起审批，传入业务数据
  approveInfoFormRef.value.open('approve', undefined, {
    bizId: selectedOrder.id,
    bizNo: selectedOrder.orderNo,
    bizType: 'sale_order'
  })
}


const handlePrint = async () => {
  if(!selectedRows.value || selectedRows.value.length === 0) {
    message.error('请选择要打印的订单！')
    return
  }

  // 如果选中了多个订单，提示用户
  if(selectedRows.value.length > 1) {
    message.warning('当前只支持单个订单打印，请选择一个订单进行打印')
    return
  }

  // 设置当前行为选中的第一个订单
  const selectedOrder = selectedRows.value[0]
  currentRow.value = selectedOrder

  // 发起打印，传入业务数据
  // 打开这个链接http://localhost:48080/jmreport/view/1107563690072690688?token=a4821ff2e0d3436d9f2474c9a1f61cd9&tenantId=1
  let url = 'http://localhost:48080/jmreport/view/1107563690072690688?'
  // 获取系统token
  url += 'token=' + getAccessToken()
  // 拼接租户ID
  url += '&tenantId=' + getTenantId()
  url += '&id=' + selectedOrder.id
  // 打开新窗口
  window.open(url)

}

/** 初始化 **/
onMounted(async() => {
  await getList()
  await getUnitMap()
  await getUserList()
})
</script>

<style scoped lang="scss">
/* 订单编号容器样式 */
.order-no-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 4px 0;
}

.order-no-link {
  color: #409eff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  flex-shrink: 0;

  &:hover {
    color: #66b1ff;
    text-decoration: underline;
  }

  &:active {
    color: #3a8ee6;
  }
}

/* 状态标签容器样式 */
.status-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

/* 状态标签样式 */
/* 订单号行布局 */
.order-no-cell {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 20px;
  padding-right: 24px; /* 为复制按钮预留空间 */
}

.order-no-content {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 复制按钮样式 */
.copy-btn {
  padding: 2px !important;
  height: 20px !important;
  min-height: 20px !important;
  width: 20px !important;
  font-size: 12px;
  opacity: 0.6;
  transition: opacity 0.2s;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.copy-btn-fixed {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}

.copy-btn:hover {
  opacity: 1;
}

.status-tag :deep(.el-tag) {
  font-size: 9px !important;
  padding: 1px 4px !important;
  height: 16px !important;
  line-height: 14px !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  flex-shrink: 0;
}
</style>
